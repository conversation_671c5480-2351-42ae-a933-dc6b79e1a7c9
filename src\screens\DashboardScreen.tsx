import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {<PERSON><PERSON><PERSON>, PieChart} from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

const screenWidth = Dimensions.get('window').width;

interface CarbonData {
  transport: number;
  energy: number;
  food: number;
  consumption: number;
}

const DashboardScreen: React.FC = () => {
  const [carbonData] = useState<CarbonData>({
    transport: 2.5,
    energy: 1.8,
    food: 1.2,
    consumption: 0.8,
  });

  const [weeklyData] = useState([2.1, 2.3, 1.9, 2.5, 2.2, 1.8, 2.0]);
  const [totalFootprint, setTotalFootprint] = useState(0);

  useEffect(() => {
    const total = Object.values(carbonData).reduce(
      (sum, value) => sum + value,
      0,
    );
    setTotalFootprint(total);
  }, [carbonData]);

  const pieData = [
    {
      name: 'Transport',
      population: carbonData.transport,
      color: '#FF6B6B',
      legendFontColor: '#333',
      legendFontSize: 12,
    },
    {
      name: 'Energy',
      population: carbonData.energy,
      color: '#4ECDC4',
      legendFontColor: '#333',
      legendFontSize: 12,
    },
    {
      name: 'Food',
      population: carbonData.food,
      color: '#45B7D1',
      legendFontColor: '#333',
      legendFontSize: 12,
    },
    {
      name: 'Shopping',
      population: carbonData.consumption,
      color: '#96CEB4',
      legendFontColor: '#333',
      legendFontSize: 12,
    },
  ];

  const lineData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: weeklyData,
        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
        strokeWidth: 3,
      },
    ],
  };

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 1,
    color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#4CAF50',
    },
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header Card */}
      <LinearGradient colors={['#4CAF50', '#45A049']} style={styles.headerCard}>
        <View style={styles.headerContent}>
          <Text style={styles.welcomeText}>Welcome back!</Text>
          <Text style={styles.footprintText}>Today's Carbon Footprint</Text>
          <Text style={styles.footprintValue}>
            {totalFootprint.toFixed(1)} kg CO₂
          </Text>
          <View style={styles.comparisonContainer}>
            <Icon name="trending-down" size={16} color="#fff" />
            <Text style={styles.comparisonText}>15% lower than yesterday</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Icon name="eco" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>127</Text>
          <Text style={styles.statLabel}>Eco Points</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="local-fire-department" size={24} color="#FF5722" />
          <Text style={styles.statValue}>7</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        <View style={styles.statCard}>
          <Icon name="trending-up" size={24} color="#2196F3" />
          <Text style={styles.statValue}>23%</Text>
          <Text style={styles.statLabel}>Improvement</Text>
        </View>
      </View>

      {/* Weekly Trend Chart */}
      <View style={styles.chartCard}>
        <Text style={styles.chartTitle}>Weekly Carbon Footprint Trend</Text>
        <LineChart
          data={lineData}
          width={screenWidth - 40}
          height={200}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
        />
      </View>

      {/* Carbon Breakdown */}
      <View style={styles.chartCard}>
        <Text style={styles.chartTitle}>Today's Breakdown</Text>
        <PieChart
          data={pieData}
          width={screenWidth - 40}
          height={200}
          chartConfig={chartConfig}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
          style={styles.chart}
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton}>
            <Icon name="add" size={24} color="#4CAF50" />
            <Text style={styles.actionText}>Log Activity</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Icon name="lightbulb" size={24} color="#FF9800" />
            <Text style={styles.actionText}>Get Tips</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerCard: {
    margin: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerContent: {
    alignItems: 'center',
  },
  welcomeText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  footprintText: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
  },
  footprintValue: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  comparisonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  comparisonText: {
    color: '#fff',
    fontSize: 14,
    marginLeft: 4,
    opacity: 0.9,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  chartCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 16,
  },
  actionsContainer: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  actionText: {
    fontSize: 14,
    color: '#333',
    marginTop: 8,
    fontWeight: '500',
  },
});

export default DashboardScreen;
