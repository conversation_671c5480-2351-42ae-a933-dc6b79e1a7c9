import AsyncStorage from '@react-native-async-storage/async-storage';
import {Activity, CarbonFootprint} from '../types';
import {CarbonCalculator} from '../utils/carbonCalculator';

export class CarbonFootprintService {
  private static readonly STORAGE_KEYS = {
    ACTIVITIES: 'ecoai_activities',
    CARBON_FOOTPRINTS: 'ecoai_carbon_footprints',
    USER_PROFILE: 'ecoai_user_profile',
  };

  // Activity Management
  static async saveActivity(activity: Activity): Promise<void> {
    try {
      const existingActivities = await this.getActivities();
      const updatedActivities = [activity, ...existingActivities];
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.ACTIVITIES,
        JSON.stringify(updatedActivities),
      );
    } catch (error) {
      console.error('Error saving activity:', error);
      throw error;
    }
  }

  static async getActivities(): Promise<Activity[]> {
    try {
      const activitiesJson = await AsyncStorage.getItem(
        this.STORAGE_KEYS.ACTIVITIES,
      );
      if (activitiesJson) {
        const activities = JSON.parse(activitiesJson);
        return activities.map((activity: any) => ({
          ...activity,
          timestamp: new Date(activity.timestamp),
        }));
      }
      return [];
    } catch (error) {
      console.error('Error getting activities:', error);
      return [];
    }
  }

  static async getActivitiesByDate(date: Date): Promise<Activity[]> {
    try {
      const allActivities = await this.getActivities();
      const targetDate = new Date(date);
      targetDate.setHours(0, 0, 0, 0);
      const nextDay = new Date(targetDate);
      nextDay.setDate(nextDay.getDate() + 1);

      return allActivities.filter(activity => {
        const activityDate = new Date(activity.timestamp);
        return activityDate >= targetDate && activityDate < nextDay;
      });
    } catch (error) {
      console.error('Error getting activities by date:', error);
      return [];
    }
  }

  static async deleteActivity(activityId: string): Promise<void> {
    try {
      const existingActivities = await this.getActivities();
      const updatedActivities = existingActivities.filter(
        activity => activity.id !== activityId,
      );
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.ACTIVITIES,
        JSON.stringify(updatedActivities),
      );
    } catch (error) {
      console.error('Error deleting activity:', error);
      throw error;
    }
  }

  // Carbon Footprint Calculation
  static async calculateDailyFootprint(date: Date): Promise<CarbonFootprint> {
    try {
      const activities = await this.getActivitiesByDate(date);
      
      const categorizedActivities = {
        transport: activities.filter(a => a.category === 'Transport'),
        energy: activities.filter(a => a.category === 'Energy'),
        food: activities.filter(a => a.category === 'Food'),
        shopping: activities.filter(a => a.category === 'Shopping'),
      };

      const transport = categorizedActivities.transport.reduce(
        (sum, activity) => sum + activity.carbonFootprint,
        0,
      );
      const energy = categorizedActivities.energy.reduce(
        (sum, activity) => sum + activity.carbonFootprint,
        0,
      );
      const food = categorizedActivities.food.reduce(
        (sum, activity) => sum + activity.carbonFootprint,
        0,
      );
      const consumption = categorizedActivities.shopping.reduce(
        (sum, activity) => sum + activity.carbonFootprint,
        0,
      );

      const total = transport + energy + food + consumption;

      const footprint: CarbonFootprint = {
        id: `${date.toISOString().split('T')[0]}_footprint`,
        userId: 'current_user', // In a real app, this would be the actual user ID
        date,
        transport,
        energy,
        food,
        consumption,
        total,
      };

      await this.saveCarbonFootprint(footprint);
      return footprint;
    } catch (error) {
      console.error('Error calculating daily footprint:', error);
      throw error;
    }
  }

  static async saveCarbonFootprint(footprint: CarbonFootprint): Promise<void> {
    try {
      const existingFootprints = await this.getCarbonFootprints();
      const updatedFootprints = existingFootprints.filter(
        f => f.id !== footprint.id,
      );
      updatedFootprints.push(footprint);
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.CARBON_FOOTPRINTS,
        JSON.stringify(updatedFootprints),
      );
    } catch (error) {
      console.error('Error saving carbon footprint:', error);
      throw error;
    }
  }

  static async getCarbonFootprints(): Promise<CarbonFootprint[]> {
    try {
      const footprintsJson = await AsyncStorage.getItem(
        this.STORAGE_KEYS.CARBON_FOOTPRINTS,
      );
      if (footprintsJson) {
        const footprints = JSON.parse(footprintsJson);
        return footprints.map((footprint: any) => ({
          ...footprint,
          date: new Date(footprint.date),
        }));
      }
      return [];
    } catch (error) {
      console.error('Error getting carbon footprints:', error);
      return [];
    }
  }

  static async getWeeklyFootprints(endDate: Date): Promise<CarbonFootprint[]> {
    try {
      const allFootprints = await this.getCarbonFootprints();
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - 6);
      startDate.setHours(0, 0, 0, 0);
      
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);

      return allFootprints.filter(footprint => {
        const footprintDate = new Date(footprint.date);
        return footprintDate >= startDate && footprintDate <= endDateTime;
      });
    } catch (error) {
      console.error('Error getting weekly footprints:', error);
      return [];
    }
  }

  static async getMonthlyAverage(month: number, year: number): Promise<number> {
    try {
      const allFootprints = await this.getCarbonFootprints();
      const monthlyFootprints = allFootprints.filter(footprint => {
        const footprintDate = new Date(footprint.date);
        return (
          footprintDate.getMonth() === month &&
          footprintDate.getFullYear() === year
        );
      });

      if (monthlyFootprints.length === 0) {
        return 0;
      }

      const totalFootprint = monthlyFootprints.reduce(
        (sum, footprint) => sum + footprint.total,
        0,
      );

      return totalFootprint / monthlyFootprints.length;
    } catch (error) {
      console.error('Error getting monthly average:', error);
      return 0;
    }
  }

  // Progress Tracking
  static async getProgressData(days: number = 30): Promise<{
    current: number;
    previous: number;
    improvement: number;
  }> {
    try {
      const endDate = new Date();
      const midDate = new Date();
      midDate.setDate(midDate.getDate() - days);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days * 2);

      const allFootprints = await this.getCarbonFootprints();
      
      const currentPeriod = allFootprints.filter(footprint => {
        const footprintDate = new Date(footprint.date);
        return footprintDate >= midDate && footprintDate <= endDate;
      });

      const previousPeriod = allFootprints.filter(footprint => {
        const footprintDate = new Date(footprint.date);
        return footprintDate >= startDate && footprintDate < midDate;
      });

      const currentAvg = currentPeriod.length > 0
        ? currentPeriod.reduce((sum, f) => sum + f.total, 0) / currentPeriod.length
        : 0;

      const previousAvg = previousPeriod.length > 0
        ? previousPeriod.reduce((sum, f) => sum + f.total, 0) / previousPeriod.length
        : 0;

      const improvement = previousAvg > 0
        ? ((previousAvg - currentAvg) / previousAvg) * 100
        : 0;

      return {
        current: currentAvg,
        previous: previousAvg,
        improvement,
      };
    } catch (error) {
      console.error('Error getting progress data:', error);
      return {current: 0, previous: 0, improvement: 0};
    }
  }

  // Data Management
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.ACTIVITIES,
        this.STORAGE_KEYS.CARBON_FOOTPRINTS,
      ]);
    } catch (error) {
      console.error('Error clearing data:', error);
      throw error;
    }
  }

  static async exportData(): Promise<string> {
    try {
      const activities = await this.getActivities();
      const footprints = await this.getCarbonFootprints();
      
      const exportData = {
        activities,
        footprints,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }
}
