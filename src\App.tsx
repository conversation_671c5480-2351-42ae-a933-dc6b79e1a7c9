import React, {useEffect, useState} from 'react';
import {StatusBar, View, Text, StyleSheet} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import screens
import DashboardScreen from './screens/DashboardScreen';
import TrackingScreen from './screens/TrackingScreen';
import RecommendationsScreen from './screens/RecommendationsScreen';
import ProfileScreen from './screens/ProfileScreen';
import AchievementsScreen from './screens/AchievementsScreen';

// Import services
import {GamificationService} from './services/GamificationService';
import {RecommendationsService} from './services/RecommendationsService';
import {CarbonFootprintService} from './services/CarbonFootprintService';
import {DataSeedingService} from './services/DataSeedingService';

const Tab = createBottomTabNavigator();

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize services when app starts
    const initializeApp = async () => {
      try {
        setIsLoading(true);
        setInitError(null);

        // Initialize all services
        await Promise.all([
          GamificationService.initializeUserStats(),
          RecommendationsService.initializeRecommendations(),
          CarbonFootprintService.initializeStorage(),
        ]);

        // Seed sample data for demonstration (only on first run)
        await DataSeedingService.seedSampleData();

        console.log('App initialized successfully');
      } catch (error) {
        console.error('Error initializing app:', error);
        setInitError('Failed to initialize app. Please restart.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Loading screen
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="eco" size={64} color="#4CAF50" />
        <Text style={styles.loadingText}>EcoAI</Text>
        <Text style={styles.loadingSubtext}>Initializing your eco journey...</Text>
      </View>
    );
  }

  // Error screen
  if (initError) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error" size={64} color="#F44336" />
        <Text style={styles.errorText}>Oops!</Text>
        <Text style={styles.errorSubtext}>{initError}</Text>
      </View>
    );
  }

  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" backgroundColor="#4CAF50" />
      <Tab.Navigator
        screenOptions={({route}) => ({
          tabBarIcon: ({color, size}) => {
            let iconName: string;

            switch (route.name) {
              case 'Dashboard':
                iconName = 'dashboard';
                break;
              case 'Tracking':
                iconName = 'track-changes';
                break;
              case 'Recommendations':
                iconName = 'lightbulb';
                break;
              case 'Achievements':
                iconName = 'emoji-events';
                break;
              case 'Profile':
                iconName = 'person';
                break;
              default:
                iconName = 'help';
            }

            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#4CAF50',
          tabBarInactiveTintColor: 'gray',
          headerStyle: {
            backgroundColor: '#4CAF50',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        })}>
        <Tab.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={{title: 'EcoAI Dashboard'}}
        />
        <Tab.Screen
          name="Tracking"
          component={TrackingScreen}
          options={{title: 'Carbon Tracking'}}
        />
        <Tab.Screen
          name="Recommendations"
          component={RecommendationsScreen}
          options={{title: 'Eco Tips'}}
        />
        <Tab.Screen
          name="Achievements"
          component={AchievementsScreen}
          options={{title: 'Achievements'}}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileScreen}
          options={{title: 'Profile'}}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 16,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#F44336',
    marginTop: 16,
  },
  errorSubtext: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default App;
