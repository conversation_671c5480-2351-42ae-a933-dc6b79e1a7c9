import React, {useEffect} from 'react';
import {StatusBar} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import screens
import DashboardScreen from './screens/DashboardScreen';
import TrackingScreen from './screens/TrackingScreen';
import RecommendationsScreen from './screens/RecommendationsScreen';
import ProfileScreen from './screens/ProfileScreen';

// Import services
import {GamificationService} from './services/GamificationService';
import {RecommendationsService} from './services/RecommendationsService';

const Tab = createBottomTabNavigator();

const App: React.FC = () => {
  useEffect(() => {
    // Initialize services when app starts
    const initializeApp = async () => {
      try {
        await GamificationService.initializeUserStats();
        await RecommendationsService.initializeRecommendations();
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();
  }, []);

  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" backgroundColor="#4CAF50" />
      <Tab.Navigator
        screenOptions={({route}) => ({
          tabBarIcon: ({color, size}) => {
            let iconName: string;

            switch (route.name) {
              case 'Dashboard':
                iconName = 'dashboard';
                break;
              case 'Tracking':
                iconName = 'track-changes';
                break;
              case 'Recommendations':
                iconName = 'lightbulb';
                break;
              case 'Profile':
                iconName = 'person';
                break;
              default:
                iconName = 'help';
            }

            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#4CAF50',
          tabBarInactiveTintColor: 'gray',
          headerStyle: {
            backgroundColor: '#4CAF50',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        })}>
        <Tab.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={{title: 'EcoAI Dashboard'}}
        />
        <Tab.Screen
          name="Tracking"
          component={TrackingScreen}
          options={{title: 'Carbon Tracking'}}
        />
        <Tab.Screen
          name="Recommendations"
          component={RecommendationsScreen}
          options={{title: 'Eco Tips'}}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileScreen}
          options={{title: 'Profile'}}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
};

export default App;
