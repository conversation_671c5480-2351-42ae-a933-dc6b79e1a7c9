// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  location: string;
  householdSize: number;
  primaryTransport: string;
  dietType: string;
  energyProvider: string;
  createdAt: Date;
  updatedAt: Date;
}

// Carbon Footprint Types
export interface CarbonFootprint {
  id: string;
  userId: string;
  date: Date;
  transport: number;
  energy: number;
  food: number;
  consumption: number;
  total: number;
}

// Activity Types
export interface Activity {
  id: string;
  userId: string;
  category: 'Transport' | 'Energy' | 'Food' | 'Shopping';
  description: string;
  amount: number;
  unit: string;
  carbonFootprint: number;
  timestamp: Date;
}

// Recommendation Types
export interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: 'Transport' | 'Energy' | 'Food' | 'Shopping';
  impact: 'High' | 'Medium' | 'Low';
  effort: 'Easy' | 'Medium' | 'Hard';
  carbonSaving: number;
  icon: string;
  completed: boolean;
  userId?: string;
}

// Gamification Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  unlockedAt?: Date;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  target: number;
  progress: number;
  participants: number;
  reward: number;
}

// Navigation Types
export type RootTabParamList = {
  Dashboard: undefined;
  Tracking: undefined;
  Recommendations: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface LineChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

export interface PieChartData {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}
