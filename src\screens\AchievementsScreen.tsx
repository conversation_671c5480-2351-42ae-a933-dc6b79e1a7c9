import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import {Achievement} from '../types';
import {GamificationService} from '../services/GamificationService';
import AchievementCard from '../components/AchievementCard';

const AchievementsScreen: React.FC = () => {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [unlockedAchievements, setUnlockedAchievements] = useState<string[]>([]);
  const [userStats, setUserStats] = useState({
    totalPoints: 0,
    rank: 'Eco Newbie',
    level: 1,
  });
  const [filter, setFilter] = useState<'all' | 'unlocked' | 'locked'>('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [achievementsData, unlockedData, statsData] = await Promise.all([
        GamificationService.getAchievements(),
        GamificationService.getUnlockedAchievements(),
        GamificationService.getUserStats(),
      ]);

      setAchievements(achievementsData);
      setUnlockedAchievements(unlockedData);
      if (statsData) {
        setUserStats({
          totalPoints: statsData.totalPoints,
          rank: statsData.rank,
          level: statsData.level,
        });
      }
    } catch (error) {
      console.error('Error loading achievements data:', error);
    }
  };

  const filteredAchievements = achievements.filter(achievement => {
    const isUnlocked = unlockedAchievements.includes(achievement.id);
    switch (filter) {
      case 'unlocked':
        return isUnlocked;
      case 'locked':
        return !isUnlocked;
      default:
        return true;
    }
  });

  const unlockedCount = achievements.filter(achievement =>
    unlockedAchievements.includes(achievement.id),
  ).length;

  const totalPoints = achievements
    .filter(achievement => unlockedAchievements.includes(achievement.id))
    .reduce((sum, achievement) => sum + achievement.points, 0);

  const renderAchievement = ({item}: {item: Achievement}) => (
    <AchievementCard
      achievement={item}
      isUnlocked={unlockedAchievements.includes(item.id)}
    />
  );

  const renderFilterButton = (
    filterType: 'all' | 'unlocked' | 'locked',
    label: string,
    count: number,
  ) => (
    <TouchableOpacity
      style={[styles.filterButton, filter === filterType && styles.activeFilter]}
      onPress={() => setFilter(filterType)}>
      <Text
        style={[
          styles.filterText,
          filter === filterType && styles.activeFilterText,
        ]}>
        {label} ({count})
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#4CAF50', '#45A049']} style={styles.header}>
        <Text style={styles.headerTitle}>Achievements</Text>
        <Text style={styles.headerSubtitle}>
          {unlockedCount}/{achievements.length} unlocked
        </Text>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Icon name="emoji-events" size={24} color="#fff" />
            <Text style={styles.statValue}>{userStats.level}</Text>
            <Text style={styles.statLabel}>Level</Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="stars" size={24} color="#fff" />
            <Text style={styles.statValue}>{totalPoints}</Text>
            <Text style={styles.statLabel}>Points</Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="military-tech" size={24} color="#fff" />
            <Text style={styles.statValue}>{unlockedCount}</Text>
            <Text style={styles.statLabel}>Badges</Text>
          </View>
        </View>

        <View style={styles.rankContainer}>
          <Text style={styles.rankLabel}>Current Rank</Text>
          <Text style={styles.rankValue}>{userStats.rank}</Text>
        </View>
      </LinearGradient>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          Progress: {Math.round((unlockedCount / achievements.length) * 100)}%
        </Text>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {width: `${(unlockedCount / achievements.length) * 100}%`},
            ]}
          />
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {renderFilterButton('all', 'All', achievements.length)}
        {renderFilterButton('unlocked', 'Unlocked', unlockedCount)}
        {renderFilterButton(
          'locked',
          'Locked',
          achievements.length - unlockedCount,
        )}
      </View>

      {/* Achievements List */}
      <FlatList
        data={filteredAchievements}
        renderItem={renderAchievement}
        keyExtractor={item => item.id}
        style={styles.achievementsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerSubtitle: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statLabel: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.9,
    marginTop: 2,
  },
  rankContainer: {
    alignItems: 'center',
    marginTop: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 12,
  },
  rankLabel: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
  },
  rankValue: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  progressContainer: {
    padding: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: -20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  progressText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'space-around',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#fff',
    elevation: 1,
  },
  activeFilter: {
    backgroundColor: '#4CAF50',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#fff',
  },
  achievementsList: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
});

export default AchievementsScreen;
